"use client";

import { useRef, useState, useEffect } from 'react';

const Contact = () => {
  const sectionRef = useRef(null);

  return (
    <section
      ref={sectionRef}
      data-section="contact"
      className="bg-background min-h-screen py-20 relative z-20"
    >
      <div className="max-w-6xl mx-auto px-6">
        {/* Contact Header */}
        <div className="text-center mb-16">
          <h2 className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl mb-6">
            Let's Connect
          </h2>
          <p className="text-secondary text-lg max-w-2xl mx-auto">
            Ready to bring your vision to life? Let's discuss your project and create something amazing together.
          </p>
        </div>

        {/* Contact Content */}
        <div className="grid md:grid-cols-2 gap-12 mb-20">
          {/* Contact Info */}
          <div className="space-y-8">
            <div>
              <h3 className="font-heading font-bold text-secondary text-2xl mb-4">
                Get In Touch
              </h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-6 h-6 bg-secondary rounded-full flex items-center justify-center">
                    <span className="text-primary text-sm">@</span>
                  </div>
                  <span className="text-secondary"><EMAIL></span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <button className="w-full bg-secondary text-primary py-2.5 px-4 rounded-lg font-semibold hover:bg-secondary/90 transition-colors">
                Request Project
              </button>
              <button className="w-full bg-primary border border-secondary text-secondary py-2.5 px-4 rounded-lg font-semibold hover:bg-secondary hover:text-primary transition-colors">
                View Resume
              </button>
            </div>

            <div>
              <h3 className="font-heading font-bold text-secondary text-2xl mb-4">
                Follow Me
              </h3>
              <div className="flex space-x-4">
                <a href="#" className="w-12 h-12 bg-primary border border-secondary/20 rounded-lg flex items-center justify-center hover:bg-secondary hover:text-primary transition-colors">
                  <span className="text-sm font-bold">LI</span>
                </a>
                <a href="#" className="w-12 h-12 bg-primary border border-secondary/20 rounded-lg flex items-center justify-center hover:bg-secondary hover:text-primary transition-colors">
                  <span className="text-sm font-bold">GH</span>
                </a>
                <a href="#" className="w-12 h-12 bg-primary border border-secondary/20 rounded-lg flex items-center justify-center hover:bg-secondary hover:text-primary transition-colors">
                  <span className="text-sm font-bold">TW</span>
                </a>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="bg-primary rounded-2xl border border-secondary/20 p-8">
            <h3 className="font-heading font-bold text-secondary text-2xl mb-6">
              Send a Message
            </h3>
            <form className="space-y-6">
              <div>
                <label className="block text-secondary text-sm font-medium mb-2">
                  Name
                </label>
                <input
                  type="text"
                  className="w-full px-4 py-3 bg-background border border-secondary/20 rounded-lg text-secondary placeholder-secondary/50 focus:outline-none focus:border-secondary"
                  placeholder="Your name"
                />
              </div>
              <div>
                <label className="block text-secondary text-sm font-medium mb-2">
                  Email
                </label>
                <input
                  type="email"
                  className="w-full px-4 py-3 bg-background border border-secondary/20 rounded-lg text-secondary placeholder-secondary/50 focus:outline-none focus:border-secondary"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-secondary text-sm font-medium mb-2">
                  Message
                </label>
                <textarea
                  rows={4}
                  className="w-full px-4 py-3 bg-background border border-secondary/20 rounded-lg text-secondary placeholder-secondary/50 focus:outline-none focus:border-secondary resize-none"
                  placeholder="Tell me about your project..."
                ></textarea>
              </div>
              <button
                type="submit"
                className="w-full bg-secondary text-primary py-3 rounded-lg font-semibold hover:bg-secondary/90 transition-colors"
              >
                Send Message
              </button>
            </form>
          </div>
        </div>

        {/* Future Retro Console Area - Placeholder */}
        <div className="text-center py-16 border-t border-secondary/20">
          <div className="bg-primary rounded-2xl border border-secondary/20 p-12 max-w-2xl mx-auto">
            <h3 className="font-heading font-bold text-secondary text-2xl mb-4">
              🎮 Coming Soon
            </h3>
            <p className="text-secondary text-lg mb-6">
              "Thanks for reaching the end! I have a little treat for you..."
            </p>
            <div className="bg-background rounded-lg p-8 border border-secondary/20">
              <p className="text-secondary/70 text-sm">
                Retro Console Games Area<br/>
                <span className="text-xs">While you're here, care for a game?</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
